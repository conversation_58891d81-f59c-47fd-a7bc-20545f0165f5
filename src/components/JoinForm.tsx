"use client";

import { useState } from "react";
import { makeApiCall, validation } from "@/utils/api-helpers";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface RoomData {
  token: string;
  roomName: string;
  serverUrl: string;
  participantName: string;
}

interface JoinFormProps {
  onJoinSuccess: (roomData: RoomData) => void;
}

export default function JoinForm({ onJoinSuccess }: JoinFormProps) {
  const [participantName, setParticipantName] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const joinInterview = async () => {
    // Validate participant name using shared validation
    const nameValidation = validation.participantName(participantName);
    if (!nameValidation.isValid) {
      setError(nameValidation.error || "Invalid name");
      return;
    }

    setIsCreating(true);
    setError(null);

    // Use shared API call utility
    const result = await makeApiCall<RoomData>("/api/join-interview", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ participantName: participantName.trim() }),
    });

    if (result.success && result.data) {
      onJoinSuccess(result.data);
    } else {
      setError(result.error || "Failed to join interview. Please try again.");
    }

    setIsCreating(false);
  };

  return (
    <div className="min-h-[calc(100vh-80px)] bg-background flex items-center justify-center">
      <div className="max-w-2xl mx-auto text-center p-6">
        <h1 className="text-3xl font-bold text-foreground mb-6">
          Start Interview Call
        </h1>

        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="mb-6">
              <Label htmlFor="participantName" className="mb-2">
                Your Name
              </Label>
              <Input
                id="participantName"
                type="text"
                value={participantName}
                onChange={(e) => setParticipantName(e.target.value)}
                placeholder="Enter your name for the interview"
                className="h-12"
                onKeyDown={(e) => e.key === "Enter" && joinInterview()}
              />
            </div>

            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <Button
              onClick={joinInterview}
              disabled={isCreating || !participantName.trim()}
              className="w-full h-12"
              size="lg"
            >
              {isCreating ? "Joining Interview..." : "Join Interview"}
            </Button>
          </CardContent>
        </Card>

        <div className="space-y-4">
          <Button asChild variant="outline">
            <a href="/questionnaire-prompt-builder">← Back to Prompt Builder</a>
          </Button>

          <div className="text-foreground/50">
            <p>Make sure to configure your interview prompt first.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
