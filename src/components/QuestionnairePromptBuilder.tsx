"use client";

import { useState, useEffect } from "react";
import { makeApiCall, validation } from "@/utils/api-helpers";
import {
  DEFAULT_INTERVIEW_PROMPT,
  PROMPT_PLACEHOLDER,
} from "@/constants/prompts";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function QuestionnairePromptBuilder() {
  const [prompt, setPrompt] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<"idle" | "success" | "error">(
    "idle"
  );
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [loadError, setLoadError] = useState<string>("");

  // Load existing prompt on component mount
  useEffect(() => {
    const loadPrompt = async () => {
      setLoadError("");

      const result = await makeApiCall("/api/questionnaire-prompt-builder");

      if (result.success) {
        setPrompt((result.data?.prompt as string) || "");
      } else {
        setLoadError(result.error || "Failed to load prompt");
        console.error("Failed to load prompt:", result.error);
      }

      setIsLoading(false);
    };

    loadPrompt();
  }, []);

  const handleSave = async () => {
    setIsSaving(true);
    setSaveStatus("idle");
    setErrorMessage("");

    const result = await makeApiCall("/api/questionnaire-prompt-builder", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ prompt }),
    });

    if (result.success) {
      setSaveStatus("success");
      console.log("Prompt saved successfully:", result.data?.message as string);
      setTimeout(() => setSaveStatus("idle"), 3000);
    } else {
      setErrorMessage(result.error || "Save failed");
      setSaveStatus("error");
      console.error("Failed to save prompt:", result.error);
    }

    setIsSaving(false);
  };

  // Retry function for failed saves
  const handleRetry = () => {
    const promptValidation = validation.prompt(prompt);
    if (promptValidation.canSave && !isSaving) {
      handleSave();
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.metaKey || e.ctrlKey) && e.key === "s") {
      e.preventDefault();
      const promptValidation = validation.prompt(prompt);
      if (promptValidation.canSave && !isSaving) {
        handleSave();
      }
    }
  };

  const handleReset = () => {
    setPrompt(DEFAULT_INTERVIEW_PROMPT);
    setSaveStatus("idle");
    setErrorMessage("");
  };

  if (isLoading) {
    return (
      <div className="min-h-[calc(100vh-80px)] bg-background flex items-center justify-center">
        <div className="text-foreground">Loading...</div>
      </div>
    );
  }

  // Show load error if prompt failed to load
  if (loadError) {
    return (
      <div className="min-h-[calc(100vh-80px)] bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Questionnaire Prompt Builder
            </h1>
            <p className="text-foreground/70">
              Create and customize the AI interviewer prompt that will guide the
              interview conversation.
            </p>
          </div>

          <Alert variant="destructive">
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <AlertDescription className="flex flex-col gap-4">
              <div>
                <h3 className="font-semibold mb-2">Failed to Load Prompt</h3>
                <p>{loadError}</p>
              </div>
              <Button
                onClick={() => window.location.reload()}
                variant="destructive"
                size="sm"
                className="self-start"
              >
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="min-h-[calc(100vh-80px)] bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Questionnaire Prompt Builder
            </h1>
            <p className="text-foreground/70">
              Create and customize the AI interviewer prompt that will guide the
              interview conversation.
            </p>
          </div>

          <Card>
            <CardContent className="pt-2">
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <Label htmlFor="prompt">Interview Prompt</Label>
                  <span
                    className={`text-sm ${
                      validation.prompt(prompt).characterCountClass
                    }`}
                  >
                    {prompt.length} characters
                    {prompt.length > 10000 && " (exceeds limit)"}
                    {prompt.length > 9000 &&
                      prompt.length <= 10000 &&
                      " (approaching limit)"}
                  </span>
                </div>
                <Textarea
                  id="prompt"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder={PROMPT_PLACEHOLDER}
                  className="h-64 resize-vertical min-h-[200px]"
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex gap-3">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={handleSave}
                        disabled={
                          isSaving || !validation.prompt(prompt).canSave
                        }
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        {isSaving ? "Saving..." : "Save Prompt"}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{validation.prompt(prompt).tooltip}</p>
                    </TooltipContent>
                  </Tooltip>

                  <Button
                    onClick={handleReset}
                    variant="secondary"
                    className="bg-gray-600 hover:bg-gray-700 text-white"
                  >
                    Reset to Default
                  </Button>
                </div>

                {saveStatus === "success" && (
                  <div className="flex items-center gap-2 text-green-600 font-medium">
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    Prompt saved successfully!
                  </div>
                )}

                {saveStatus === "error" && (
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2 text-red-600 font-medium">
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                      {errorMessage ||
                        "Failed to save prompt. Please try again."}
                    </div>
                    <Button
                      onClick={handleRetry}
                      disabled={isSaving || !validation.prompt(prompt).canSave}
                      variant="destructive"
                      size="sm"
                    >
                      Retry
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
            <h2 className="text-lg font-semibold text-foreground mb-3">
              💡 Prompt Writing Tips
            </h2>
            <ul className="space-y-2 text-foreground/80">
              <li>• Be specific about the interview style and tone you want</li>
              <li>
                • Include instructions for how to handle different types of
                responses
              </li>
              <li>• Specify the types of questions the AI should ask</li>
              <li>• Consider including guidelines for follow-up questions</li>
              <li>• Think about how the AI should conclude the interview</li>
            </ul>
          </div>

          <div className="mt-6 text-center">
            <a
              href="/call"
              className="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 
                       text-white font-medium rounded-lg transition-colors"
            >
              Start Interview Call →
            </a>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
}
